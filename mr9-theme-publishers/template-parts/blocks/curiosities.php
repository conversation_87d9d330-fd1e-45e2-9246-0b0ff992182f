<section class="curiosities-block">
  <h2 class="block-title">Curiosidades</h2>
  <div class="curiosity-list">
    <?php
    $query = new WP_Query([
      'category_name'  => 'curiosidades',
      'posts_per_page' => 4
    ]);
    if ( $query->have_posts() ) :
      while ( $query->have_posts() ) : $query->the_post(); ?>
        <article class="curiosity-card" <?php post_class(); ?>>
          <a href="<?php the_permalink(); ?>">
            <?php if ( has_post_thumbnail() ) : ?>
              <div class="thumb"><?php the_post_thumbnail('medium'); ?></div>
            <?php endif; ?>
            <div class="info">
              <h3 class="title"><?php the_title(); ?></h3>
              <time datetime="<?php echo get_the_date('c'); ?>"><?php echo get_the_date(); ?></time>
            </div>
          </a>
        </article>
      <?php endwhile;
      wp_reset_postdata();
    else :
      echo '<p>Nenhum conteúdo curioso ainda.</p>';
    endif;
    ?>
  </div>
</section>

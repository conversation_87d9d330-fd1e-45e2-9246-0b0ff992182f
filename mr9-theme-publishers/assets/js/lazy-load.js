document.addEventListener("DOMContentLoaded", () => {
  const loadMoreBtn = document.getElementById("load-more");
  const container = document.getElementById("latest-posts");

  if (loadMoreBtn) {
    loadMoreBtn.addEventListener("click", () => {
      const page = loadMoreBtn.dataset.page;

      fetch(`/wp-json/mr9/v1/load-posts?page=${page}`)
        .then(res => res.text())
        .then(html => {
          container.insertAdjacentHTML("beforeend", html);
          loadMoreBtn.dataset.page = parseInt(page) + 1;
        })
        .catch(err => {
          console.error("Erro ao carregar mais posts", err);
          loadMoreBtn.remove();
        });
    });
  }
});

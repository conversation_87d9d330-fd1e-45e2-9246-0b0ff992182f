document.addEventListener('DOMContentLoaded', function() {
    // Social sharing functionality
    const socialButtons = document.querySelectorAll('.social-btn');
    
    socialButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // Add click tracking or analytics here if needed
            console.log('Social share clicked:', this.classList[1]);
            
            // Add visual feedback
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
    
    // Smooth scroll behavior for sticky elements
    let lastScrollTop = 0;
    const socialSidebar = document.querySelector('.social-share-sticky');
    
    if (socialSidebar) {
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            // Add fade effect based on scroll position
            if (scrollTop > 300) {
                socialSidebar.style.opacity = '1';
            } else {
                socialSidebar.style.opacity = '0.7';
            }
            
            lastScrollTop = scrollTop;
        });
    }
    
    // Copy link functionality (could be added as additional social option)
    function copyToClipboard(text) {
        if (navigator.clipboard) {
            navigator.clipboard.writeText(text).then(function() {
                showNotification('Link copiado!');
            });
        } else {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showNotification('Link copiado!');
        }
    }
    
    // Simple notification system
    function showNotification(message) {
        const notification = document.createElement('div');
        notification.textContent = message;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #7CD950;
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            z-index: 9999;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // Enhanced popular posts interaction
    const popularPosts = document.querySelectorAll('.popular-post-link');
    
    popularPosts.forEach(link => {
        link.addEventListener('mouseenter', function() {
            this.style.transform = 'translateX(5px)';
        });
        
        link.addEventListener('mouseleave', function() {
            this.style.transform = 'translateX(0)';
        });
    });
    
    // Reading progress indicator (optional enhancement)
    function createReadingProgress() {
        const article = document.querySelector('.single-content');
        if (!article) return;
        
        const progressBar = document.createElement('div');
        progressBar.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: #7CD950;
            z-index: 9999;
            transition: width 0.1s ease;
        `;
        
        document.body.appendChild(progressBar);
        
        window.addEventListener('scroll', function() {
            const articleTop = article.offsetTop;
            const articleHeight = article.offsetHeight;
            const windowHeight = window.innerHeight;
            const scrollTop = window.pageYOffset;
            
            const articleBottom = articleTop + articleHeight;
            const windowBottom = scrollTop + windowHeight;
            
            if (scrollTop >= articleTop && scrollTop <= articleBottom) {
                const progress = ((scrollTop - articleTop) / (articleHeight - windowHeight)) * 100;
                progressBar.style.width = Math.min(Math.max(progress, 0), 100) + '%';
            }
        });
    }
    
    // Initialize reading progress
    createReadingProgress();
    
    // Print functionality
    function addPrintButton() {
        const singleFooter = document.querySelector('.single-footer');
        if (!singleFooter) return;
        
        const printButton = document.createElement('button');
        printButton.innerHTML = '🖨️ Imprimir artigo';
        printButton.style.cssText = `
            background: #f8f9fa;
            border: 1px solid #e5e5e5;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin-top: 20px;
            transition: all 0.3s ease;
        `;
        
        printButton.addEventListener('click', function() {
            window.print();
        });
        
        printButton.addEventListener('mouseenter', function() {
            this.style.background = '#e9ecef';
        });
        
        printButton.addEventListener('mouseleave', function() {
            this.style.background = '#f8f9fa';
        });
        
        singleFooter.appendChild(printButton);
    }
    
    // Initialize print button
    addPrintButton();
});

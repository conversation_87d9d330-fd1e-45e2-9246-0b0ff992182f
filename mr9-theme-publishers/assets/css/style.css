/* Header Styles */
.site-header {
  background: #0B3B4F;
  color: #fff;
  font-family: 'Inter', sans-serif;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 999;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Header scroll states */
.site-header.header-hidden {
  transform: translateY(-100%);
}

.site-header.header-scrolled {
  box-shadow: 0 4px 20px rgba(0,0,0,0.15);
  background: rgba(11, 59, 79, 0.95);
  backdrop-filter: blur(10px);
}

/* Body padding to compensate for fixed header */
body {
  padding-top: 120px; /* Adjust based on header height */
}

@media (max-width: 768px) {
  body {
    padding-top: 100px;
  }
}

@media (max-width: 480px) {
  body {
    padding-top: 90px;
  }
}

/* Header Top Row */
.header-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid;
  border-image: linear-gradient(269deg, rgba(57, 117, 151, 0) 3.58%, #406880 50.52%, rgba(57, 117, 151, 0) 96.82%) 1;
}

/* Menu Trigger */
.menu-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.menu-trigger:hover {
  background-color: rgba(255,255,255,0.1);
}

.hamburger {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.hamburger span {
  width: 18px;
  height: 2px;
  background-color: #fff;
  border-radius: 1px;
  transition: all 0.3s;
}

.menu-text {
  font-size: 14px;
  font-weight: 500;
  color: #fff;
}

/* Site Logo */
.site-logo {
  flex: 1;
  display: flex;
  justify-content: center;
}

.site-logo a {
  display: block;
}

.site-logo img {
  height: 32px;
  width: auto;
}

/* Search Container */
.search-container {
  position: relative;
}

.search-btn {
  background: none;
  border: none;
  color: #fff;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background-color: rgba(255,255,255,0.1);
}

.search-btn svg {
  stroke: #fff;
}

/* Desktop Search Form */
.desktop-search-form {
  position: absolute;
  top: 100%;
  right: 0;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  min-width: 400px;
  z-index: 1000;
  margin-top: 8px;
  overflow: hidden;
}

.search-form-advanced {
  width: 100%;
}

.search-input-container {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.search-input-container input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.search-input-container input:focus {
  outline: none;
  border-color: #7CD950;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(124, 217, 80, 0.1);
}

.search-submit-btn {
  background: #7CD950;
  color: #fff;
  border: none;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  margin-left: 12px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-submit-btn:hover {
  background: #5fb83a;
  transform: translateY(-1px);
}

/* Search Suggestions */
.search-suggestions {
  padding: 20px;
}

.search-categories,
.recent-searches {
  margin-bottom: 20px;
}

.search-categories:last-child,
.recent-searches:last-child {
  margin-bottom: 0;
}

.search-suggestions h4 {
  color: #333;
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 12px 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.category-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.category-tag {
  background: #f0f7ff;
  color: #0B3B4F;
  text-decoration: none;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.category-tag:hover {
  background: #7CD950;
  color: #fff;
  transform: translateY(-1px);
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.recent-item {
  color: #666;
  text-decoration: none;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
}

.recent-item:hover {
  background: #f8f9fa;
  color: #0B3B4F;
}

.recent-item::before {
  content: "🕒";
  margin-right: 8px;
  font-size: 12px;
}

/* Mobile Search Form */
.mobile-search-container {
  padding: 16px 20px 20px 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.mobile-search-form {
  width: 100%;
}

.mobile-search-wrapper {
  display: flex;
  align-items: center;
  background: rgba(255,255,255,0.1);
  border: 1px solid rgba(255,255,255,0.2);
  border-radius: 25px;
  padding: 12px 20px;
  gap: 12px;
  transition: all 0.3s ease;
}

.mobile-search-wrapper:focus-within {
  border-color: rgba(255,255,255,0.4);
  background: rgba(255,255,255,0.15);
}

.search-icon {
  color: rgba(255,255,255,0.6);
  flex-shrink: 0;
}

.mobile-search-wrapper input {
  flex: 1;
  background: transparent;
  border: none;
  color: #fff;
  font-size: 16px;
  outline: none;
  font-weight: 400;
}

.mobile-search-wrapper input::placeholder {
  color: rgba(255,255,255,0.6);
}

/* Header Navigation */
.header-nav {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.header-nav::-webkit-scrollbar {
  display: none;
}

/* Navigation Arrows */
.nav-arrow {
  background: none;
  border: none;
  color: #fff;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
  flex-shrink: 0;
}

.nav-arrow:hover {
  background-color: rgba(255,255,255,0.1);
}

/* Main Navigation */
.main-navigation {
  flex: 1;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
  scroll-behavior: smooth;
}

.main-navigation::-webkit-scrollbar {
  display: none;
}

.main-menu-list {
  display: flex;
  align-items: center;
  gap: 24px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.main-menu-list li {
  position: relative;
  white-space: nowrap;
}

.main-menu-list a {
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 12px;
  border-radius: 4px;
  transition: all 0.2s;
  display: block;
}

.main-menu-list a:hover {
  background-color: rgba(255,255,255,0.1);
  color: #7CD950;
}

/* Submenu Styles */
.main-menu-list .sub-menu {
  position: absolute;
  top: 100%;
  left: 0;
  background: #0B3B4F;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
  min-width: 200px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
  z-index: 1000;
  list-style: none;
  margin: 0;
  padding: 8px 0;
}

.main-menu-list li:hover .sub-menu {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.main-menu-list .sub-menu li {
  width: 100%;
}

.main-menu-list .sub-menu a {
  padding: 10px 16px;
  font-size: 13px;
  border-radius: 0;
}

.main-menu-list .sub-menu a:hover {
  background-color: rgba(124, 217, 80, 0.1);
}

/* TV Live Button */
.tv-live-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: rgba(124, 217, 80, 0.2);
  color: #fff;
  text-decoration: none;
  font-size: 14px;
  font-weight: 600;
  padding: 8px 16px;
  border-radius: 20px;
  border: 1px solid #7CD950;
  transition: all 0.2s;
  flex-shrink: 0;
}

.tv-live-btn:hover {
  background: #7CD950;
  color: #0B3B4F;
}

.live-indicator {
  width: 8px;
  height: 8px;
  background: #7CD950;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Mobile Menu Overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.8);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

.mobile-menu-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 280px;
  height: 100%;
  background: #0B3B4F;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.mobile-menu-overlay.active .mobile-menu-content {
  transform: translateX(0);
}

.mobile-menu-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 20px 16px 20px;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  flex-shrink: 0;
}

.mobile-menu-header span {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  color: #fff;
  cursor: pointer;
  padding: 4px;
}



/* Mobile Navigation */
.mobile-nav {
  position: relative;
  overflow: hidden;
  flex: 1;
}

.mobile-menu-list {
  list-style: none;
  margin: 0;
  padding: 0;
  transition: transform 0.3s ease;
}

.mobile-menu-list li {
  position: relative;
}

.mobile-menu-list > li > a,
.mobile-menu-list > li > .submenu-trigger {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #fff;
  text-decoration: none;
  padding: 16px 20px;
  font-size: 16px;
  font-weight: 400;
  border-bottom: 1px solid rgba(255,255,255,0.1);
  transition: background-color 0.2s ease;
}

.mobile-menu-list > li > a:hover,
.mobile-menu-list > li > .submenu-trigger:hover {
  background-color: rgba(255,255,255,0.05);
}

/* Submenu trigger styling */
.submenu-trigger {
  background: none;
  border: none;
  cursor: pointer;
  width: 100%;
  text-align: left;
}

.submenu-arrow {
  color: rgba(255,255,255,0.6);
  transition: transform 0.3s ease;
}

/* TV Live link styling */
.tv-live-link {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.live-indicator {
  width: 8px;
  height: 8px;
  background: #ff4444;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Mobile Submenu */
.mobile-submenu {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(100vh - 80px);
  background: #0B3B4F;
  list-style: none;
  margin: 0;
  padding: 0;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  display: none;
  overflow-y: auto;
  z-index: 10;
}

/* Submenu back button */
.submenu-back {
  border-bottom: 1px solid rgba(255,255,255,0.1);
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #fff;
  padding: 16px 20px;
  font-size: 16px;
  cursor: pointer;
  width: 100%;
  text-align: left;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: rgba(255,255,255,0.05);
}

/* Submenu main link */
.submenu-main {
  color: #7CD950 !important;
  font-weight: 600 !important;
  background: rgba(124, 217, 80, 0.1) !important;
}

/* Submenu items */
.mobile-submenu li:not(.submenu-back) a {
  display: block;
  color: #B8C5D1;
  text-decoration: none;
  padding: 14px 20px;
  font-size: 15px;
  border-bottom: 1px solid rgba(255,255,255,0.05);
  transition: all 0.2s ease;
}

.mobile-submenu li:not(.submenu-back) a:hover {
  background-color: rgba(124, 217, 80, 0.1);
  color: #7CD950;
}

.tv-live-mobile {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(124, 217, 80, 0.2);
  border: 1px solid #7CD950;
  margin-top: 16px;
}

.live-dot {
  width: 8px;
  height: 8px;
  background: #7CD950;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .header-nav {
    padding: 8px 0;
  }

  .main-menu-list {
    gap: 16px;
  }

  .main-menu-list a {
    font-size: 13px;
    padding: 6px 10px;
  }

  .tv-live-btn {
    font-size: 13px;
    padding: 6px 12px;
  }

  .nav-arrow {
    padding: 6px;
  }
}

@media (max-width: 480px) {
  .header-top {
    padding: 10px 0;
  }

  .site-logo img {
    height: 28px;
  }

  .menu-text {
    font-size: 13px;
  }

  .main-menu-list {
    gap: 12px;
  }

  .main-menu-list a {
    font-size: 12px;
    padding: 6px 8px;
  }

  .tv-live-btn {
    font-size: 12px;
    padding: 6px 10px;
  }
}

/* Additional Header Enhancements */
.site-logo svg {
  transition: transform 0.2s ease;
}

.site-logo:hover svg {
  transform: scale(1.05);
}

/* Smooth scrolling for navigation - merged with main navigation styles above */

/* Focus states for accessibility */
.menu-trigger:focus,
.search-btn:focus,
.nav-arrow:focus,
.main-menu-list a:focus,
.mobile-menu-list a:focus,
.tv-live-btn:focus,
.desktop-search-form input:focus,
.mobile-search-form input:focus,
.submenu-toggle:focus {
  outline: 2px solid #7CD950;
  outline-offset: 2px;
}

/* Loading state for navigation arrows */
.nav-arrow:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

/* Better mobile menu backdrop */
.mobile-menu-overlay {
  backdrop-filter: blur(4px);
}

/* Header z-index already defined above */

.post-grid {
  display: grid;
  gap: 24px;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  margin-top: 2rem;
}

.post-card {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}

.post-card:hover {
  transform: translateY(-3px);
}

.post-card a {
  text-decoration: none;
  color: inherit;
  display: block;
}

.post-card .thumb img {
  width: 100%;
  display: block;
  height: auto;
}

.post-card .post-info {
  padding: 16px;
}

.post-card .title {
  font-size: 1.125rem;
  margin: 0 0 0.5rem;
}

.post-card .excerpt {
  color: #555;
  font-size: 0.95rem;
  line-height: 1.4;
}

.content-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 32px;
}

main.container {
  flex: 1 1 700px;
}

.sidebar {
  flex: 0 0 300px;
  background: #f9f9f9;
  padding: 16px;
  border-radius: 6px;
}

.widget {
  margin-bottom: 2rem;
}

.widget-title {
  font-size: 1.1rem;
  margin-bottom: 0.75rem;
  border-bottom: 2px solid #eee;
}

.single-post .featured-image img {
  width: 100%;
  border-radius: 6px;
  margin-bottom: 1.5rem;
}

.entry-header {
  margin-bottom: 1rem;
}

.entry-title {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.post-meta {
  color: #777;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
}

.entry-content {
  font-size: 1.05rem;
  line-height: 1.7;
}

.entry-content p {
  margin-bottom: 1.5rem;
}

.entry-content blockquote {
  border-left: 4px solid #ccc;
  padding-left: 1rem;
  color: #555;
  font-style: italic;
  margin: 1.5rem 0;
}

.entry-content a {
  color: #0066cc;
  text-decoration: underline;
}

.post-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 2rem;
}

.post-navigation a {
  color: #333;
  text-decoration: none;
  font-weight: bold;
}

.recommended-block {
  margin: 3rem auto;
}
.block-title {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  border-bottom: 2px solid #ccc;
  padding-bottom: .5rem;
}
.recommended-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.recommended-card {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}
.recommended-card:hover {
  transform: translateY(-2px);
}
.recommended-card a {
  color: inherit;
  text-decoration: none;
  display: block;
}
.recommended-card .thumb img {
  width: 100%;
  height: auto;
  display: block;
}
.recommended-card .info {
  padding: 12px;
}
.recommended-card .title {
  font-size: 1.05rem;
  margin: 0 0 .5rem;
}
.recommended-card time {
  font-size: 0.85rem;
  color: #666;
}

.latest-posts-block {
  margin: 3rem auto;
}
.latest-grid {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}
.latest-card {
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}
.latest-card:hover {
  transform: translateY(-2px);
}
.latest-card a {
  color: inherit;
  text-decoration: none;
  display: block;
}
.latest-card .thumb img {
  width: 100%;
  height: auto;
  display: block;
}
.latest-card .info {
  padding: 12px;
}
.latest-card .title {
  font-size: 1.05rem;
  margin: 0 0 .5rem;
}
.latest-card time {
  font-size: 0.85rem;
  color: #666;
}
.load-more {
  margin: 2rem auto 0;
  display: block;
  background: #0b3d5c;
  color: #fff;
  font-size: 0.95rem;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 4px;
  cursor: pointer;
}

.curiosities-block {
  margin: 3rem auto;
}
.curiosity-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}
.curiosity-card {
  display: flex;
  background: #fff;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 6px rgba(0,0,0,0.05);
  transition: transform 0.2s;
}
.curiosity-card:hover {
  transform: scale(1.01);
}
.curiosity-card a {
  color: inherit;
  text-decoration: none;
  display: flex;
  width: 100%;
}
.curiosity-card .thumb img {
  width: 160px;
  height: 100%;
  object-fit: cover;
  display: block;
}
.curiosity-card .info {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.curiosity-card .title {
  font-size: 1.05rem;
  margin: 0 0 .5rem;
}
.curiosity-card time {
  font-size: 0.85rem;
  color: #666;
}
@media (max-width: 600px) {
  .curiosity-card {
    flex-direction: column;
  }
  .curiosity-card .thumb img {
    width: 100%;
    height: auto;
  }
}

.site-wrapper {
  width: 100%;
}

/* Footer Styles */
.site-footer {
  background: #2C4A5C;
  color: #fff;
  padding: 60px 0 0;
  margin-top: 3rem;
  font-family: 'Inter', sans-serif;
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  margin-bottom: 60px;
}

/* Footer Brand */
.footer-brand {
  max-width: 500px;
}

.footer-logo h2 {
  font-size: 48px;
  font-weight: bold;
  margin: 0 0 24px 0;
  line-height: 1;
}

.logo-the {
  color: #7CD950;
}

.logo-agribiz {
  color: #fff;
}

.footer-description p {
  color: #B8C5D1;
  font-size: 16px;
  line-height: 1.6;
  margin: 0 0 24px 0;
}

.partner-link {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: #fff;
  text-decoration: none;
  font-size: 16px;
  font-weight: 500;
  padding: 12px 24px;
  border: 1px solid rgba(255,255,255,0.3);
  border-radius: 6px;
  transition: all 0.3s ease;
  margin-bottom: 32px;
}

.partner-link:hover {
  background: rgba(124, 217, 80, 0.1);
  border-color: #7CD950;
  color: #7CD950;
}

.footer-social {
  display: flex;
  gap: 16px;
}

.footer-social a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: rgba(255,255,255,0.1);
  border-radius: 8px;
  color: #B8C5D1;
  transition: all 0.3s ease;
}

.footer-social a:hover {
  background: #7CD950;
  color: #2C4A5C;
  transform: translateY(-2px);
}

/* Footer Menu */
.footer-menu h3 {
  color: #fff;
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 32px 0;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(255,255,255,0.2);
}

.footer-menu-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px 32px;
}

.footer-menu-column {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.footer-menu-column a {
  color: #B8C5D1;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-menu-column a:hover {
  color: #7CD950;
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px 0;
  border-top: 1px solid rgba(255,255,255,0.2);
}

.footer-bottom-left p {
  color: #B8C5D1;
  font-size: 14px;
  margin: 0;
}

.footer-bottom-links {
  display: flex;
  gap: 32px;
}

.footer-bottom-links a {
  color: #B8C5D1;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-bottom-links a:hover {
  color: #7CD950;
}

.footer-bottom-logo img {
  height: 24px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

.footer-bottom-logo:hover img {
  opacity: 1;
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 40px;
    margin-bottom: 40px;
  }

  .footer-logo h2 {
    font-size: 36px;
  }

  .footer-menu-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .footer-bottom {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }

  .footer-bottom-links {
    flex-wrap: wrap;
    justify-content: center;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .site-footer {
    padding: 40px 0 0;
  }

  .footer-logo h2 {
    font-size: 28px;
  }

  .footer-description p {
    font-size: 14px;
  }

  .footer-social {
    justify-content: center;
  }

  .footer-bottom-links {
    gap: 12px;
  }

  .footer-bottom-links a {
    font-size: 12px;
  }
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 16px;
}
/* Remove old header styles that conflict */
.site-footer {
  width: 100%;
  background-color: #002e47;
  color: #fff;
}

.single-layout {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
  margin-top: 2rem;
}
.sidebar {
  background: #f9f9f9;
  padding: 1rem;
}
.sidebar .widget {
  margin-bottom: 1.5rem;
}
.sidebar .widget-title {
  font-weight: bold;
  margin-bottom: 0.75rem;
}
@media (max-width: 768px) {
  .single-layout {
    grid-template-columns: 1fr;
  }
  .sidebar {
    padding: 0;
  }
}

.container.single-layout {
  display: grid;
  grid-template-columns: 1fr 320px;
  gap: 40px;
}
.sidebar {
  width: 100%;
}
@media (max-width: 768px) {
  .container.single-layout {
    grid-template-columns: 1fr;
  }
}

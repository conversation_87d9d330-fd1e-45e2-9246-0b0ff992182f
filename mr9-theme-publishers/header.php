<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
  <meta charset="<?php bloginfo('charset'); ?>">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script src="https://cdn.tailwindcss.com"></script>
  <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<!-- Menu Off-Canvas -->
<div id="mobileMenu" class="mobile-menu-overlay">
  <div class="mobile-menu-content">
    <div class="mobile-menu-header">
      <span>Menu</span>
      <button onclick="toggleMobileMenu()" aria-label="Fechar menu" class="close-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    </div>

    <!-- Busca no menu mobile -->
    <div class="mobile-search" onclick="toggleMobileSearch()">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35M10 18a8 8 0 100-16 8 8 0 000 16z"/>
      </svg>
      <span>Buscar</span>
    </div>

    <!-- Formulário de busca mobile -->
    <div id="mobileSearchForm" class="mobile-search-form" style="display: none;">
      <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
        <input type="search" name="s" placeholder="Digite sua busca..." value="<?php echo get_search_query(); ?>" required>
        <button type="submit">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35M10 18a8 8 0 100-16 8 8 0 000 16z"/>
          </svg>
        </button>
      </form>
    </div>

    <!-- Menu mobile -->
    <?php
    wp_nav_menu([
      'theme_location' => 'primary',
      'container' => 'nav',
      'container_class' => 'mobile-nav',
      'menu_class' => 'mobile-menu-list',
      'fallback_cb' => 'mr9_mobile_fallback_menu',
      'walker' => new MR9_Mobile_Walker_Nav_Menu()
    ]);
    ?>
  </div>
</div>

<script>
  function toggleMobileMenu() {
    const menu = document.getElementById('mobileMenu');
    menu.classList.toggle('active');
  }

  function toggleMobileSearch() {
    const searchForm = document.getElementById('mobileSearchForm');
    if (searchForm.style.display === 'none' || searchForm.style.display === '') {
      searchForm.style.display = 'block';
      searchForm.querySelector('input').focus();
    } else {
      searchForm.style.display = 'none';
    }
  }

  function toggleDesktopSearch() {
    const searchForm = document.getElementById('desktopSearchForm');
    if (searchForm.style.display === 'none' || searchForm.style.display === '') {
      searchForm.style.display = 'block';
      searchForm.querySelector('input').focus();
    } else {
      searchForm.style.display = 'none';
    }
  }

  function toggleSubmenu(element) {
    const submenu = element.parentNode.nextElementSibling;
    if (submenu && submenu.classList.contains('mobile-sub-menu')) {
      if (submenu.style.display === 'none' || submenu.style.display === '') {
        submenu.style.display = 'block';
        element.textContent = '-';
      } else {
        submenu.style.display = 'none';
        element.textContent = '+';
      }
    }
  }

  // Navigation scroll functionality
  document.addEventListener('DOMContentLoaded', function() {
    const navContainer = document.querySelector('.main-menu-list');
    const prevBtn = document.querySelector('.nav-prev');
    const nextBtn = document.querySelector('.nav-next');

    if (navContainer && prevBtn && nextBtn) {
      prevBtn.addEventListener('click', function() {
        navContainer.scrollBy({
          left: -200,
          behavior: 'smooth'
        });
      });

      nextBtn.addEventListener('click', function() {
        navContainer.scrollBy({
          left: 200,
          behavior: 'smooth'
        });
      });

      // Update arrow visibility based on scroll position
      function updateArrows() {
        const isAtStart = navContainer.scrollLeft <= 0;
        const isAtEnd = navContainer.scrollLeft >= navContainer.scrollWidth - navContainer.clientWidth;

        prevBtn.style.opacity = isAtStart ? '0.5' : '1';
        nextBtn.style.opacity = isAtEnd ? '0.5' : '1';
      }

      navContainer.addEventListener('scroll', updateArrows);
      updateArrows(); // Initial check
    }

    // Close search forms when clicking outside
    document.addEventListener('click', function(e) {
      const mobileSearchForm = document.getElementById('mobileSearchForm');
      const desktopSearchForm = document.getElementById('desktopSearchForm');
      const searchBtns = document.querySelectorAll('.search-btn, .mobile-search');

      let clickedSearchBtn = false;
      searchBtns.forEach(btn => {
        if (btn.contains(e.target)) {
          clickedSearchBtn = true;
        }
      });

      if (!clickedSearchBtn) {
        if (mobileSearchForm && !mobileSearchForm.contains(e.target)) {
          mobileSearchForm.style.display = 'none';
        }
        if (desktopSearchForm && !desktopSearchForm.contains(e.target)) {
          desktopSearchForm.style.display = 'none';
        }
      }
    });
  });
</script>

<div class="site-wrapper">
<header class="site-header">
  <div class="container">
    <!-- Header Top Row -->
    <div class="header-top">
      <div class="menu-trigger" onclick="toggleMobileMenu()">
        <div class="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span class="menu-text">Menu</span>
      </div>

      <div class="site-logo">
        <a href="<?php echo esc_url(home_url('/')); ?>">
          <svg width="180" height="40" viewBox="0 0 180 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <text x="50" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#7CD950" text-anchor="middle">CANAL</text>
            <text x="130" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#FFFFFF" text-anchor="middle">RURAL</text>
          </svg>
        </a>
      </div>

      <div class="search-container">
        <button class="search-btn" aria-label="Buscar" onclick="toggleDesktopSearch()">
          <svg width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35M10 18a8 8 0 100-16 8 8 0 000 16z"/>
          </svg>
        </button>

        <!-- Formulário de busca desktop -->
        <div id="desktopSearchForm" class="desktop-search-form" style="display: none;">
          <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
            <input type="search" name="s" placeholder="Digite sua busca..." value="<?php echo get_search_query(); ?>" required>
            <button type="submit">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35M10 18a8 8 0 100-16 8 8 0 000 16z"/>
              </svg>
            </button>
          </form>
        </div>
      </div>
    </div>

    <!-- Header Bottom Row - Navigation -->
    <div class="header-nav">
      <button class="nav-arrow nav-prev" aria-label="Anterior">
        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
          <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
        </svg>
      </button>

      <?php
      wp_nav_menu([
        'theme_location' => 'primary',
        'container' => 'nav',
        'container_class' => 'main-navigation',
        'menu_class' => 'main-menu-list',
        'fallback_cb' => 'mr9_main_fallback_menu',
        'walker' => new MR9_Main_Walker_Nav_Menu()
      ]);
      ?>

      <a href="<?php echo esc_url(home_url('/tv-ao-vivo')); ?>" class="tv-live-btn">
        <span class="live-indicator"></span>
        TV ao vivo
      </a>

      <button class="nav-arrow nav-next" aria-label="Próximo">
        <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
          <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
        </svg>
      </button>
    </div>
  </div>
</header>

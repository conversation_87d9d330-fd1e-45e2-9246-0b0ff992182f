<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
  <meta charset="<?php bloginfo('charset'); ?>">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script src="https://cdn.tailwindcss.com"></script>
  <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<!-- Menu Off-Canvas -->
<div id="mobileMenu" class="mobile-menu-overlay">
  <div class="mobile-menu-content">
    <div class="mobile-menu-header">
      <a href="<?php echo esc_url(home_url('/')); ?>" class="mobile-logo">
        <svg width="140" height="32" viewBox="0 0 140 32" fill="none" xmlns="http://www.w3.org/2000/svg">
          <text x="35" y="20" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#7CD950" text-anchor="middle">CANAL</text>
          <text x="105" y="20" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#FFFFFF" text-anchor="middle">RURAL</text>
        </svg>
      </a>
      <button onclick="toggleMobileMenu()" aria-label="Fechar menu" class="close-btn">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
        </svg>
      </button>
    </div>

    <!-- Busca no menu mobile -->
    <div class="mobile-search-container">
      <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>" class="mobile-search-form">
        <div class="mobile-search-wrapper">
          <svg class="search-icon" xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35M10 18a8 8 0 100-16 8 8 0 000 16z"/>
          </svg>
          <input type="search" name="s" placeholder="Buscar" value="<?php echo get_search_query(); ?>">
        </div>
      </form>
    </div>

    <!-- Menu mobile -->
    <div class="mobile-nav">
      <?php
      wp_nav_menu([
        'theme_location' => 'primary',
        'container' => false,
        'menu_class' => 'mobile-menu-list',
        'fallback_cb' => 'mr9_mobile_fallback_menu',
        'walker' => new MR9_Mobile_Walker_Nav_Menu()
      ]);
      ?>
    </div>
  </div>
</div>

<script>
  function toggleMobileMenu() {
    const menu = document.getElementById('mobileMenu');
    menu.classList.toggle('active');
  }



  function toggleDesktopSearch() {
    const searchForm = document.getElementById('desktopSearchForm');
    const suggestions = document.getElementById('searchSuggestions');

    if (searchForm.style.display === 'none' || searchForm.style.display === '') {
      searchForm.style.display = 'block';
      suggestions.style.display = 'block';
      searchForm.querySelector('input').focus();
      loadRecentSearches();
    } else {
      searchForm.style.display = 'none';
      suggestions.style.display = 'none';
    }
  }

  // Search functionality
  function loadRecentSearches() {
    const recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    const recentContainer = document.getElementById('recentSearches');
    const recentList = recentContainer.querySelector('.recent-list');

    if (recentSearches.length > 0) {
      recentContainer.style.display = 'block';
      recentList.innerHTML = recentSearches.slice(0, 5).map(search =>
        `<a href="/?s=${encodeURIComponent(search)}" class="recent-item">${search}</a>`
      ).join('');
    }
  }

  function saveSearch(query) {
    if (!query || query.length < 2) return;

    let recentSearches = JSON.parse(localStorage.getItem('recentSearches') || '[]');
    recentSearches = recentSearches.filter(search => search !== query);
    recentSearches.unshift(query);
    recentSearches = recentSearches.slice(0, 10);

    localStorage.setItem('recentSearches', JSON.stringify(recentSearches));
  }

  // Mobile submenu functions
  function openSubmenu(trigger) {
    const parentLi = trigger.closest('li');
    const submenu = parentLi.querySelector('.mobile-submenu');

    if (submenu) {
      submenu.classList.add('active');
    }
  }

  function closeSubmenu(button) {
    const submenu = button.closest('.mobile-submenu');

    if (submenu) {
      submenu.classList.remove('active');
    }
  }

  // Header scroll behavior (like Canal Rural)
  let lastScrollTop = 0;
  let isScrolling = false;

  function handleHeaderScroll() {
    const header = document.querySelector('.site-header');
    const currentScroll = window.pageYOffset || document.documentElement.scrollTop;

    if (currentScroll > lastScrollTop && currentScroll > 100) {
      // Scrolling down - hide header
      header.classList.add('header-hidden');
    } else if (currentScroll < lastScrollTop) {
      // Scrolling up - show header
      header.classList.remove('header-hidden');
    }

    // Add/remove scrolled class for styling
    if (currentScroll > 50) {
      header.classList.add('header-scrolled');
    } else {
      header.classList.remove('header-scrolled');
    }

    lastScrollTop = currentScroll <= 0 ? 0 : currentScroll;
  }

  // Main initialization
  document.addEventListener('DOMContentLoaded', function() {
    // Mobile submenu event listeners
    const submenuTriggers = document.querySelectorAll('.submenu-trigger');
    const backButtons = document.querySelectorAll('.back-button');

    submenuTriggers.forEach((trigger) => {
      trigger.addEventListener('click', function(e) {
        e.preventDefault();
        openSubmenu(this);
      });
    });

    backButtons.forEach((button) => {
      button.addEventListener('click', function(e) {
        e.preventDefault();
        closeSubmenu(this);
      });
    });

    // Navigation scroll functionality
    const navContainer = document.querySelector('.main-navigation');
    const prevBtn = document.querySelector('.nav-prev');
    const nextBtn = document.querySelector('.nav-next');

    // Header scroll behavior
    let ticking = false;

    function requestTick() {
      if (!ticking) {
        requestAnimationFrame(handleHeaderScroll);
        ticking = true;
        setTimeout(() => { ticking = false; }, 10);
      }
    }

    window.addEventListener('scroll', requestTick);

    if (navContainer && prevBtn && nextBtn) {
      prevBtn.addEventListener('click', function() {
        navContainer.scrollBy({
          left: -200,
          behavior: 'smooth'
        });
      });

      nextBtn.addEventListener('click', function() {
        navContainer.scrollBy({
          left: 200,
          behavior: 'smooth'
        });
      });

      // Update arrow visibility based on scroll position
      function updateArrows() {
        const isAtStart = navContainer.scrollLeft <= 0;
        const isAtEnd = navContainer.scrollLeft >= navContainer.scrollWidth - navContainer.clientWidth;

        prevBtn.style.opacity = isAtStart ? '0.5' : '1';
        nextBtn.style.opacity = isAtEnd ? '0.5' : '1';
      }

      navContainer.addEventListener('scroll', updateArrows);
      updateArrows(); // Initial check
    }

    // Search form submission
    const searchForms = document.querySelectorAll('form[role="search"]');
    searchForms.forEach(form => {
      form.addEventListener('submit', function(e) {
        const input = this.querySelector('input[name="s"]');
        if (input && input.value.trim()) {
          saveSearch(input.value.trim());
        }
      });
    });

    // Close search forms when clicking outside
    document.addEventListener('click', function(e) {
      const desktopSearchForm = document.getElementById('desktopSearchForm');
      const searchSuggestions = document.getElementById('searchSuggestions');
      const searchBtns = document.querySelectorAll('.search-btn');

      let clickedSearchBtn = false;
      searchBtns.forEach(btn => {
        if (btn.contains(e.target)) {
          clickedSearchBtn = true;
        }
      });

      if (!clickedSearchBtn) {
        if (desktopSearchForm && !desktopSearchForm.contains(e.target)) {
          desktopSearchForm.style.display = 'none';
          if (searchSuggestions) {
            searchSuggestions.style.display = 'none';
          }
        }
      }
    });
  });
</script>

<div class="site-wrapper">
<header class="site-header">
  <div class="container">
    <!-- Header Top Row -->
    <div class="header-top">
      <div class="menu-trigger" onclick="toggleMobileMenu()">
        <div class="hamburger">
          <span></span>
          <span></span>
          <span></span>
        </div>
        <span class="menu-text">Menu</span>
      </div>

      <div class="site-logo">
        <a href="<?php echo esc_url(home_url('/')); ?>">
          <svg width="180" height="40" viewBox="0 0 180 40" fill="none" xmlns="http://www.w3.org/2000/svg">
            <text x="50" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#7CD950" text-anchor="middle">CANAL</text>
            <text x="130" y="25" font-family="Arial, sans-serif" font-size="18" font-weight="bold" fill="#FFFFFF" text-anchor="middle">RURAL</text>
          </svg>
        </a>
      </div>

      <div class="search-container">
        <button class="search-btn" aria-label="Buscar" onclick="toggleDesktopSearch()">
          <svg width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35M10 18a8 8 0 100-16 8 8 0 000 16z"/>
          </svg>
        </button>

        <!-- Formulário de busca desktop -->
        <div id="desktopSearchForm" class="desktop-search-form" style="display: none;">
          <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>" class="search-form-advanced">
            <div class="search-input-container">
              <input type="search" name="s" placeholder="O que você está procurando?" value="<?php echo get_search_query(); ?>" required autocomplete="off">
              <button type="submit" class="search-submit-btn">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-4.35-4.35M10 18a8 8 0 100-16 8 8 0 000 16z"/>
                </svg>
              </button>
            </div>
            <div class="search-suggestions" id="searchSuggestions" style="display: none;">
              <div class="search-categories">
                <h4>Categorias populares:</h4>
                <div class="category-tags">
                  <a href="<?php echo esc_url(home_url('/categoria/agricultura')); ?>" class="category-tag">Agricultura</a>
                  <a href="<?php echo esc_url(home_url('/categoria/pecuaria')); ?>" class="category-tag">Pecuária</a>
                  <a href="<?php echo esc_url(home_url('/categoria/economia')); ?>" class="category-tag">Economia</a>
                  <a href="<?php echo esc_url(home_url('/categoria/politica')); ?>" class="category-tag">Política</a>
                </div>
              </div>
              <div class="recent-searches" id="recentSearches" style="display: none;">
                <h4>Buscas recentes:</h4>
                <div class="recent-list"></div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- Header Bottom Row - Navigation -->
    <div class="header-nav">
      <?php
      wp_nav_menu([
        'theme_location' => 'primary',
        'container' => 'nav',
        'container_class' => 'main-navigation',
        'menu_class' => 'main-menu-list',
        'fallback_cb' => 'mr9_main_fallback_menu',
        'walker' => new MR9_Main_Walker_Nav_Menu()
      ]);
      ?>

      <a href="<?php echo esc_url(home_url('/tv-ao-vivo')); ?>" class="tv-live-btn">
        <span class="live-indicator"></span>
        TV ao vivo
      </a>

      <div class="nav-arrows">
        <button class="nav-arrow nav-prev" aria-label="Anterior">
          <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
          </svg>
        </button>
        <button class="nav-arrow nav-next" aria-label="Próximo">
          <svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">
            <path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</header>

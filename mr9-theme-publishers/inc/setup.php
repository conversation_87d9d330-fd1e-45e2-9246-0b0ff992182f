<?php
add_action('after_setup_theme', function(){
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('custom-logo');
    register_nav_menus([
        'primary' => __('Menu Principal','mr9'),
    ]);
});

add_action('widgets_init', function() {
    register_sidebar([
        'name'          => __('Sidebar Principal', 'mr9'),
        'id'            => 'sidebar-1',
        'description'   => __('Adicione widgets aqui.', 'mr9'),
        'before_widget' => '<section id="%1$s" class="widget %2$s">',
        'after_widget'  => '</section>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ]);
});

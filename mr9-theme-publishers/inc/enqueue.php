<?php
add_action('wp_enqueue_scripts', function(){
  wp_enqueue_style('mr9-style', get_template_directory_uri() . '/assets/css/style.css', [], '4.0.6');
  wp_enqueue_script('mr9-lazy-load', get_template_directory_uri() . '/assets/js/lazy-load.js', [], '1.0', true);

  // Enqueue single post JavaScript only on single post pages
  if (is_single()) {
    wp_enqueue_script('mr9-single-post', get_template_directory_uri() . '/assets/js/single-post.js', [], '1.0', true);
  }
});

<?php
add_action('wp_enqueue_scripts', function(){
  // Google Fonts
  wp_enqueue_style('google-fonts', 'https://fonts.googleapis.com/css2?family=Figtree:wght@300;400;500;600;700;800;900&family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap', [], null);

  wp_enqueue_style('mr9-style', get_template_directory_uri() . '/assets/css/style.css', ['google-fonts'], '4.0.7');
  wp_enqueue_script('mr9-lazy-load', get_template_directory_uri() . '/assets/js/lazy-load.js', [], '1.0', true);

  // Enqueue single post JavaScript only on single post pages
  if (is_single()) {
    wp_enqueue_script('mr9-single-post', get_template_directory_uri() . '/assets/js/single-post.js', [], '1.0', true);
  }
});

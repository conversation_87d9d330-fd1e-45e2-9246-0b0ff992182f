<?php
require_once get_template_directory() . '/inc/setup.php';
require_once get_template_directory() . '/inc/enqueue.php';

// Custom Walker for Main Navigation with submenu support
class MR9_Main_Walker_Nav_Menu extends Walker_Nav_Menu {
  function start_lvl(&$output, $depth = 0, $args = null) {
    $indent = str_repeat("\t", $depth);
    $output .= "\n$indent<ul class=\"sub-menu\">\n";
  }

  function end_lvl(&$output, $depth = 0, $args = null) {
    $indent = str_repeat("\t", $depth);
    $output .= "$indent</ul>\n";
  }

  function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
    $indent = ($depth) ? str_repeat("\t", $depth) : '';
    $classes = empty($item->classes) ? array() : (array) $item->classes;
    $classes[] = 'menu-item-' . $item->ID;

    $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
    $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

    $id = apply_filters('nav_menu_item_id', 'menu-item-'. $item->ID, $item, $args);
    $id = $id ? ' id="' . esc_attr($id) . '"' : '';

    $output .= $indent . '<li' . $id . $class_names .'>';

    $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
    $attributes .= ! empty($item->target) ? ' target="' . esc_attr($item->target) .'"' : '';
    $attributes .= ! empty($item->xfn) ? ' rel="'    . esc_attr($item->xfn) .'"' : '';
    $attributes .= ! empty($item->url) ? ' href="'   . esc_attr($item->url) .'"' : '';

    $item_output = isset($args->before) ? $args->before : '';
    $item_output .= '<a' . $attributes .'>';
    $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
    $item_output .= '</a>';
    $item_output .= isset($args->after) ? $args->after : '';

    $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
  }

  function end_el(&$output, $item, $depth = 0, $args = null) {
    $output .= "</li>\n";
  }
}

// Custom Walker for Mobile Navigation with submenu support (Canal Rural style)
class MR9_Mobile_Walker_Nav_Menu extends Walker_Nav_Menu {
  private $parent_item = null;

  function start_lvl(&$output, $depth = 0, $args = null) {
    $indent = str_repeat("\t", $depth);
    $output .= "\n$indent<ul class=\"mobile-submenu\" data-depth=\"$depth\">\n";

    // Add back button for level 1+
    if ($depth === 0) {
      $output .= $indent . "\t<li class=\"submenu-back\">\n";
      $output .= $indent . "\t\t<button class=\"back-button\">\n";
      $output .= $indent . "\t\t\t<svg width=\"16\" height=\"16\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n";
      $output .= $indent . "\t\t\t\t<path d=\"M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z\"/>\n";
      $output .= $indent . "\t\t\t</svg>\n";
      $output .= $indent . "\t\t\tVoltar\n";
      $output .= $indent . "\t\t</button>\n";
      $output .= $indent . "\t</li>\n";

      // Add parent item link as first submenu item (Canal Rural style)
      if ($this->parent_item) {
        $output .= $indent . "\t<li>\n";
        $output .= $indent . "\t\t<a href=\"" . esc_attr($this->parent_item->url) . "\" class=\"submenu-main\">\n";
        $output .= $indent . "\t\t\tTudo sobre " . esc_html($this->parent_item->title) . "\n";
        $output .= $indent . "\t\t</a>\n";
        $output .= $indent . "\t</li>\n";
      }
    }
  }

  function end_lvl(&$output, $depth = 0, $args = null) {
    $indent = str_repeat("\t", $depth);
    $output .= $indent . "</ul>\n";
  }

  function start_el(&$output, $item, $depth = 0, $args = null, $id = 0) {
    $indent = ($depth) ? str_repeat("\t", $depth + 1) : '';
    $classes = empty($item->classes) ? array() : (array) $item->classes;
    $classes[] = 'mobile-menu-item-' . $item->ID;

    $class_names = join(' ', apply_filters('nav_menu_css_class', array_filter($classes), $item, $args));
    $class_names = $class_names ? ' class="' . esc_attr($class_names) . '"' : '';

    $id = apply_filters('nav_menu_item_id', 'mobile-menu-item-'. $item->ID, $item, $args);
    $id = $id ? ' id="' . esc_attr($id) . '"' : '';

    $has_children = in_array('menu-item-has-children', $classes);

    // Store parent item for submenu generation
    if ($has_children && $depth === 0) {
      $this->parent_item = $item;
    }

    $output .= $indent . '<li' . $id . $class_names .'>';

    $attributes = ! empty($item->attr_title) ? ' title="'  . esc_attr($item->attr_title) .'"' : '';
    $attributes .= ! empty($item->target) ? ' target="' . esc_attr($item->target) .'"' : '';
    $attributes .= ! empty($item->xfn) ? ' rel="'    . esc_attr($item->xfn) .'"' : '';
    $attributes .= ! empty($item->url) ? ' href="'   . esc_attr($item->url) .'"' : '';

    $item_output = isset($args->before) ? $args->before : '';

    if ($has_children && $depth === 0) {
      // Parent item with children at root level - use submenu-trigger class
      $item_output .= '<a href="#" class="submenu-trigger">';
      $item_output .= apply_filters('the_title', $item->title, $item->ID);
      $item_output .= '<svg class="submenu-arrow" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
      $item_output .= '<path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>';
      $item_output .= '</svg>';
      $item_output .= '</a>';
    } else {
      // Regular menu item (including submenu items)
      $item_output .= '<a' . $attributes .'>';
      $item_output .= (isset($args->link_before) ? $args->link_before : '') . apply_filters('the_title', $item->title, $item->ID) . (isset($args->link_after) ? $args->link_after : '');
      $item_output .= '</a>';
    }

    $item_output .= isset($args->after) ? $args->after : '';

    $output .= apply_filters('walker_nav_menu_start_el', $item_output, $item, $depth, $args);
  }

  function end_el(&$output, $item, $depth = 0, $args = null) {
    $indent = ($depth) ? str_repeat("\t", $depth + 1) : '';
    $output .= "</li>\n";
  }
}

// Fallback menu functions
function mr9_main_fallback_menu() {
  echo '<nav class="main-navigation">';
  echo '<ul class="main-menu-list">';
  echo '<li><a href="' . esc_url(home_url('/')) . '">Início</a></li>';
  echo '<li><a href="#">Agricultura</a></li>';
  echo '<li><a href="#">Pecuária</a></li>';
  echo '<li><a href="#">Tempo</a></li>';
  echo '<li><a href="#">Cotações</a></li>';
  echo '<li><a href="#">Empreendedorismo</a></li>';
  echo '<li><a href="#">Soja Brasil</a></li>';
  echo '<li><a href="#">Aves e Suínos</a></li>';
  echo '<li><a href="#">Economia</a></li>';
  echo '<li><a href="#">Política</a></li>';
  echo '<li><a href="#">Sustentabilidade</a></li>';
  echo '<li><a href="#">Tecnologia</a></li>';
  echo '<li><a href="#">Mercado</a></li>';
  echo '<li><a href="#">Exportação</a></li>';
  echo '<li><a href="#">Importação</a></li>';
  echo '<li><a href="#">Commodities</a></li>';
  echo '<li><a href="#">Agronegócio</a></li>';
  echo '<li><a href="#">Inovação</a></li>';
  echo '<li><a href="#">Pesquisa</a></li>';
  echo '<li><a href="#">Desenvolvimento</a></li>';
  echo '</ul>';
  echo '</nav>';
}

function mr9_mobile_fallback_menu() {
  echo '<ul class="mobile-menu-list">';
  echo '<li><a href="' . esc_url(home_url('/')) . '">Início</a></li>';

  // Agricultura com submenu
  echo '<li class="menu-item-has-children">';
  echo '<a href="#" class="submenu-trigger">';
  echo 'Agricultura';
  echo '<svg class="submenu-arrow" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
  echo '<path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>';
  echo '</svg>';
  echo '</a>';
  echo '<ul class="mobile-submenu">';
  echo '<li class="submenu-back">';
  echo '<button class="back-button">';
  echo '<svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
  echo '<path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>';
  echo '</svg>';
  echo 'Voltar';
  echo '</button>';
  echo '</li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/agricultura')) . '" class="submenu-main">Tudo sobre Agricultura</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/milho')) . '">Milho</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/trigo')) . '">Trigo</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/laranja')) . '">Laranja</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/hortifruti')) . '">Hortifrúti</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/horta-em-casa')) . '">Horta em Casa</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/feijao')) . '">Feijão</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/algodao')) . '">Algodão</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/soja')) . '">Soja</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/cana')) . '">Cana</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/arroz')) . '">Arroz</a></li>';
  echo '</ul>';
  echo '</li>';

  // Pecuária com submenu
  echo '<li class="menu-item-has-children">';
  echo '<a href="#" class="submenu-trigger">';
  echo 'Pecuária';
  echo '<svg class="submenu-arrow" width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
  echo '<path d="M8.59 16.59L10 18l6-6-6-6-1.41 1.41L13.17 12z"/>';
  echo '</svg>';
  echo '</a>';
  echo '<ul class="mobile-submenu">';
  echo '<li class="submenu-back">';
  echo '<button class="back-button">';
  echo '<svg width="16" height="16" fill="currentColor" viewBox="0 0 24 24">';
  echo '<path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>';
  echo '</svg>';
  echo 'Voltar';
  echo '</button>';
  echo '</li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/pecuaria')) . '" class="submenu-main">Tudo sobre Pecuária</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/bovinos')) . '">Bovinos</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/suinos')) . '">Suínos</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/aves')) . '">Aves</a></li>';
  echo '</ul>';
  echo '</li>';

  echo '<li><a href="' . esc_url(home_url('/categoria/tempo')) . '">Tempo</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/cotacoes')) . '">Cotações</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/empreendedorismo')) . '">Empreendedorismo</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/soja-brasil')) . '">Soja Brasil</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/economia')) . '">Economia</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/politica')) . '">Política</a></li>';
  echo '<li><a href="' . esc_url(home_url('/categoria/sustentabilidade')) . '">Sustentabilidade</a></li>';
  echo '<li><a href="' . esc_url(home_url('/tv-ao-vivo')) . '" class="tv-live-link"><span class="live-indicator"></span>TV ao vivo</a></li>';
  echo '</ul>';
}

// Lazy Load Endpoint seguro
add_action('rest_api_init', function() {
  register_rest_route('mr9/v1', '/load-posts', [
    'methods' => 'GET',
    'callback' => function( $request ) {
      $paged = $request->get_param('page') ?: 1;
      $query = new WP_Query([
        'posts_per_page' => 4,
        'paged' => $paged
      ]);
      ob_start();
      if ( $query->have_posts() ) {
        while ( $query->have_posts() ) {
          $query->the_post();
          echo '<article class="latest-card">';
          echo '<a href="' . get_permalink() . '">';
          if ( has_post_thumbnail() ) {
            echo '<div class="thumb">' . get_the_post_thumbnail(get_the_ID(), 'medium') . '</div>';
          }
          echo '<div class="info">';
          echo '<h3 class="title">' . get_the_title() . '</h3>';
          echo '<time datetime="' . get_the_date('c') . '">' . get_the_date() . '</time>';
          echo '</div>';
          echo '</a>';
          echo '</article>';
        }
        wp_reset_postdata();
      }
      return ob_get_clean();
    }
  ]);
});


// Registro de menus
function mr9_register_menus() {
    register_nav_menu('primary', __('Primary Menu', 'mr9'));
}
add_action('after_setup_theme', 'mr9_register_menus');

// Post views counter for popular posts
function mr9_set_post_views($postID) {
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if($count==''){
        $count = 0;
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
    }else{
        $count++;
        update_post_meta($postID, $count_key, $count);
    }
}

// Remove prefetch to avoid false counts
remove_action( 'wp_head', 'adjacent_posts_rel_link_wp_head', 10, 0);

// Track post views on single posts
function mr9_track_post_views ($post_id) {
    if ( !is_single() ) return;
    if ( empty ( $post_id) ) {
        global $post;
        $post_id = $post->ID;
    }
    mr9_set_post_views($post_id);
}
add_action( 'wp_head', 'mr9_track_post_views');

// Get post views count
function mr9_get_post_views($postID){
    $count_key = 'post_views_count';
    $count = get_post_meta($postID, $count_key, true);
    if($count==''){
        delete_post_meta($postID, $count_key);
        add_post_meta($postID, $count_key, '0');
        return "0";
    }
    return $count;
}

<?php
require_once get_template_directory() . '/inc/setup.php';
require_once get_template_directory() . '/inc/enqueue.php';

// Lazy Load Endpoint seguro
add_action('rest_api_init', function() {
  register_rest_route('mr9/v1', '/load-posts', [
    'methods' => 'GET',
    'callback' => function( $request ) {
      $paged = $request->get_param('page') ?: 1;
      $query = new WP_Query([
        'posts_per_page' => 4,
        'paged' => $paged
      ]);
      ob_start();
      if ( $query->have_posts() ) {
        while ( $query->have_posts() ) {
          $query->the_post();
          echo '<article class="latest-card">';
          echo '<a href="' . get_permalink() . '">';
          if ( has_post_thumbnail() ) {
            echo '<div class="thumb">' . get_the_post_thumbnail(get_the_ID(), 'medium') . '</div>';
          }
          echo '<div class="info">';
          echo '<h3 class="title">' . get_the_title() . '</h3>';
          echo '<time datetime="' . get_the_date('c') . '">' . get_the_date() . '</time>';
          echo '</div>';
          echo '</a>';
          echo '</article>';
        }
        wp_reset_postdata();
      }
      return ob_get_clean();
    }
  ]);
});


// Registro de menus
function mr9_register_menus() {
    register_nav_menu('main-menu', __('Main Menu', 'mr9'));
}
add_action('after_setup_theme', 'mr9_register_menus');
